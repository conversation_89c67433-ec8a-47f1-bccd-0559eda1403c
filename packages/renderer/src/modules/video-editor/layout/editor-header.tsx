import { useAutoSaver, useEditorContext } from '@/modules/video-editor/contexts'
import React, { useCallback } from 'react'
import { Button } from '@/components/ui/button'
import useVirtualTabsStore from '@/libs/stores/useVirtualTabsStore'
import { flatOverlaysFromTracks } from '@/modules/video-editor/utils/track-helper'
import { OverlayType } from '@clipnest/remotion-shared/types'

export function EditorHeader(): React.JSX.Element {
  const { scriptId, tracks } = useEditorContext()
  const { saveProject } = useAutoSaver()

  const { pushNamedTab } = useVirtualTabsStore()

  const navigateToMixcutPage = useCallback(async () => {
    await saveProject()
    pushNamedTab('Mixcut', { id: scriptId })

    if (!flatOverlaysFromTracks(tracks).some(o => o.type === OverlayType.VIDEO && o.durationInFrames > 0)) {
      alert('请先添加视频素材')
      return
    }
  }, [scriptId, saveProject])

  return (
    <div className="w-full">
      <header
        className="sticky top-0 flex shrink-0 items-center gap-2.5
      bg-white dark:bg-gray-900/10
      border-l
      border-b border-gray-200 dark:border-gray-800
      p-1.5 px-4.5"
      >
        <div className="flex-grow flex items-center gap-3">
          <div className="text-sm text-gray-300">{scriptId}</div>
        </div>

        <Button variant="default" onClick={navigateToMixcutPage}>
          前往混剪
        </Button>
      </header>
    </div>
  )
}
