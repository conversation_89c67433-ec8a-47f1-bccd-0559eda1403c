import React from 'react'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Label } from '@/components/ui/label'
import { Loader2 } from 'lucide-react'
import { useAllPlatforms, usePlatform, usePlatformSwitch } from '@/contexts/PlatformContext'
import { PlatformType } from '@/libs/request/api/matrix'
import { cn } from '@/components/lib/utils'

interface PlatformSelectorProps {
  className?: string
  onPlatformChange?: (platform: PlatformType) => Promise<void>
  disabled?: boolean
}

export const PlatformSelector: React.FC<PlatformSelectorProps> = ({
  className,
  onPlatformChange,
  disabled = false
}) => {
  const { currentPlatform, isLoading } = usePlatform()
  const { switchPlatform } = usePlatformSwitch()
  const allPlatforms = useAllPlatforms()

  const handlePlatformChange = async (value: string) => {
    const platform = value as PlatformType
    if (platform === currentPlatform) return

    await switchPlatform(platform, onPlatformChange)
  }

  return (
    <div className={cn('flex items-center gap-4', className)}>
      <span className="text-sm font-medium text-muted-foreground">平台:</span>

      <RadioGroup
        value={currentPlatform}
        onValueChange={handlePlatformChange}
        disabled={disabled || isLoading}
        className="flex items-center gap-6"
      >
        {allPlatforms.map(platform => (
          <div key={platform.key} className="flex items-center space-x-2">
            <RadioGroupItem
              value={platform.key}
              id={platform.key}
              disabled={disabled || isLoading}
            />
            <Label
              htmlFor={platform.key}
              className={cn(
                'flex items-center gap-2 cursor-pointer',
                (disabled || isLoading) && 'cursor-not-allowed opacity-50'
              )}
            >
              <span className="text-base">{platform.icon}</span>
              <span className="font-medium">{platform.name}</span>
            </Label>
          </div>
        ))}
      </RadioGroup>

      {isLoading && (
        <div className="flex items-center gap-2 text-xs text-muted-foreground">
          <Loader2 className="h-3 w-3 animate-spin" />
          <span>切换中...</span>
        </div>
      )}
    </div>
  )
}

export default PlatformSelector
