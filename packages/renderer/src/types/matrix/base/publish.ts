import { BaseMediaFile, BaseProduct } from './common'
import { BaseTimeSetting } from './time'

/**
 * 基础账号产品信息
 */
export interface BaseAccountProduct {
  /** 账号ID */
  accountId: number
  /** 产品列表 */
  products: BaseProduct[]
}

/**
 * 基础任务明细
 */
export interface BaseDetailDOS {
  /** 账号ID */
  accountId: string
  /** 内容封面 */
  cover: string
  /** 内容描述 */
  description: string
  /** 内容标题 */
  title: string
  /** 内容链接 */
  url: string
  /** 产品列表 */
  products?: BaseProduct[]
}

/**
 * 基础创建矩阵参数
 */
export interface BaseCreateMatrixParams {
  /** 发布账号列表 */
  accountIds: number[]
  /** 任务明细 */
  detailDOS: BaseDetailDOS[]
  /** 营销目标 */
  marketingTarget: string
  /** 计划名称 */
  name: string
  /** 发布模式 */
  publishMode: number
  /** 发布设置，1-一个账号一个内容，2-一个账号多个内容 */
  setting: number
  /** 定时模式和循环模式时的具体配置 */
  timeSetting: BaseTimeSetting
  /** 发布时间设置，1-立即，2-定时，3-循环 */
  timeType: number
  /** 标题列表 */
  titles: string[]
  /** 总账号数 */
  totalAccount: number
  /** 总内容数 */
  totalContent: number
  /** 内容列表 */
  contentList: BaseMediaFile[]
  /** 内容类型 */
  contentType?: number
}
