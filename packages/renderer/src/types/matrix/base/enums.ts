/**
 * 通用枚举定义
 * 包含所有平台共享的枚举类型
 */

/**
 * 发布设置枚举
 */
export enum BaseSetting {
  /** 一个账号一个内容 */
  ONE_ACCOUNT_ONE_CONTENT = 1,
  /** 一个账号多个内容 */
  ONE_ACCOUNT_MULTIPLE_CONTENTS = 2
}

/**
 * 发布时间类型枚举
 */
export enum BaseTimeType {
  /** 立即 */
  IMMEDIATE = 1,
  /** 定时 */
  SCHEDULED = 2,
  /** 循环 */
  LOOP = 3
}

/**
 * 定时模式间隔类型枚举
 */
export enum BasePeriodType {
  /** 单账号间隔 */
  SINGLE_ACCOUNT = 1,
  /** 多账号间隔 */
  MULTIPLE_ACCOUNTS = 2
}

/**
 * 账号状态枚举
 */
export enum BaseAccountStatus {
  /** 未授权 */
  UNAUTHORIZED = 0,
  /** 已授权 */
  AUTHORIZED = 1,
  /** 授权过期 */
  EXPIRED = 2,
  /** 授权失败 */
  FAILED = 3
}

/**
 * 发布状态枚举
 */
export enum BasePublishStatus {
  /** 待发布 */
  PENDING = 0,
  /** 发布中 */
  PUBLISHING = 1,
  /** 发布失败 */
  FAILED = 2,
  /** 发布成功 */
  SUCCESS = 3,
  /** 部分成功 */
  PARTIAL_SUCCESS = 4
}

/**
 * 内容类型枚举
 */
export enum BaseContentType {
  /** 图片 */
  IMAGE = 1,
  /** 视频 */
  VIDEO = 2,
  /** 图文 */
  IMAGE_TEXT = 3
}
