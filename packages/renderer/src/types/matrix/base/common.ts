import { PaginationParams } from '@app/shared/types/database.types'

/**
 * 基础分页搜索参数
 */
export interface BaseSearchParams extends PaginationParams {
  keyword?: string
  createTime?: number[]
  status?: number
}

/**
 * 基础时间范围参数
 */
export interface BaseTimeRangeParams {
  startTime?: number
  endTime?: number
}

/**
 * 基础产品信息
 */
export interface BaseProduct {
  title: string
  url: string
}

/**
 * 基础位置信息
 */
export interface BasePositionInfo {
  poiId: string
  poiName: string
}

/**
 * 基础媒体文件信息
 */
export interface BaseMediaFile {
  cover: string
  url?: string
  name?: string
  orgCover?: string
}

/**
 * 基础详情媒体文件信息
 */
export interface BaseDetailMediaFile extends BaseMediaFile {
  mediaId?: any
  orgTitle?: any
  duration?: any
  size?: any
  title?: any
}

/**
 * 基础账号概览统计
 */
export interface BaseAccountOverview {
  totalAccounts: number
  activeAccounts: number
  totalPublish: number
  totalLikes: number
  totalComments: number
  totalShares: number
  totalFans: number
}

/**
 * 基础推送计划
 */
export interface BasePushPlan {
  id: number
  name: string
  status: number
  statusText?: string
  totalAccount: number
  publishedCount?: number
  failedCount?: number
  createTime: number
  updateTime?: number
  marketingTarget?: string
  publishTime?: number
}

/**
 * 基础推送详情
 */
export interface BasePushDetail {
  id: number
  accountId: number
  accountName?: string
  title?: string
  url?: string
  publishTime?: number
  status: number
  statusText?: string
  createTime: number
  error?: string | null
}

/**
 * 基础发布表单详情
 */
export interface BasePublishFormDetail {
  id: number
  name: string
  channelType: number
  publishType: number
  deliverType: number
  totalAccount: number
  marketingTarget: string
  accountIds: number[]
  titles: string[]
  businessId: any
  publishMode: number
  setting: number
  timeType: number
  createTime: number
  updateTime: number
}
