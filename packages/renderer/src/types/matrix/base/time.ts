/**
 * 基础时间设置
 * 定时模式和循环模式时的具体配置
 */
export interface BaseTimeSetting {
  /**
   * 循环模式的发布日期时间戳
   */
  loopDays: number[]
  /**
   * 循环发布间隔
   */
  loopPeriod: number
  /**
   * 循环模式发布时间，格式为08:00:00
   */
  loopTime: string
  /**
   * 循环模式，每天发布内容数
   */
  numEachDay: number
  /**
   * 定时模式，间隔
   */
  period: number
  /**
   * 定时模式间隔类型，1-单账号间隔，2-多账号间隔
   */
  periodType: number[]
  /**
   * 定时模式，发布时间
   */
  publishTime: number
}
